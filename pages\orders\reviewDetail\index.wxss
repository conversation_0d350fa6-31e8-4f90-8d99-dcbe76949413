.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 100%;
}

.back-btn {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 内容区域 */
.content {
  padding: 20rpx;
  padding-top: 120rpx; /* 为固定导航栏留出空间 */
}

/* 评价卡片 */
.review-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.review-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.rating-container {
  display: flex;
  align-items: center;
}

.stars {
  display: flex;
  margin-right: 20rpx;
}

.star {
  font-size: 32rpx;
  margin-right: 5rpx;
}

.star-full {
  color: #ffa500;
}

.star-half {
  color: #ffa500;
}

.star-empty {
  color: #ddd;
}

.rating-text {
  font-size: 28rpx;
  color: #666;
}

/* 评价内容 */
.review-content {
  margin-bottom: 30rpx;
}

.content-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
}

/* 评价时间 */
.review-time {
  display: flex;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.time-label {
  font-size: 26rpx;
  color: #999;
  margin-right: 10rpx;
}

.time-text {
  font-size: 26rpx;
  color: #666;
}

/* 订单信息卡片 */
.order-info-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
