// pages/message/index.js
const utils = require('../utils/util');
const Session = require('../../common/Session').default;
const messageApi = require('../../api/modules/message').default;

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    newsList: [],
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getUserInfo();
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo) {
      return wx.redirectTo({
        url: "/pages/login/index",
      });
    }
    this.setData({ userInfo });
    this.loadMessageList();
  },

  /**
   * 加载消息列表
   */
  async loadMessageList() {
    if (this.data.loading) return;

    this.setData({ loading: true });
    try {
      const res = await messageApi.getMessageList(this.data.userInfo.id);
      const {list} = res || {};
      if (list && list.length >= 0) {
        const formattedList = list.map(item => ({
          ...item,
          time: utils.formatDate(new Date(item.createdAt)),
          icon: this.getMessageIcon(item.type)
        }));
        this.setData({ newsList: formattedList });
      }
    } catch (error) {
      console.error('获取消息列表失败:', error);
      wx.showToast({
        title: '获取消息失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 根据消息类型获取图标
   */
  getMessageIcon(type) {
    const iconMap = {
      'system': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
      'platform': '//xian7.zos.ctyun.cn/pet/static/msg2.png',
      'order': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
    };
    return iconMap[type] || '//xian7.zos.ctyun.cn/pet/static/msg1.png';
  },

  /**
   * 点击消息项
   */
  onMessageTap(e) {
    const { index } = e.currentTarget.dataset;
    const message = this.data.newsList[index];
    if (message) {
      // 标记为已读
      this.markAsRead(message.id, index);
      // 跳转到详情页
      wx.navigateTo({
        url: `/pages/message/messageDetail/index?id=${message.id}`
      });
    }
  },

  /**
   * 标记消息为已读
   */
  async markAsRead(messageId, index) {
    try {
      await messageApi.markAsRead(messageId);
      // 更新本地数据
      const newsList = [...this.data.newsList];
      if (newsList[index]) {
        newsList[index].isRead = true;
        newsList[index].count = 0;
        this.setData({ newsList });
      }
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新消息列表
    if (this.data.userInfo) {
      this.loadMessageList();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadMessageList().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 暂不实现分页加载
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '消息中心',
      path: '/pages/message/index'
    };
  }
})