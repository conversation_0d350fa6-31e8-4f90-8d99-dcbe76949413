.container {
  background-color: #f4f4f4;
  padding: 20rpx;
  font-family: Arial, sans-serif;
}

.order-header {
  text-align: center;
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.order-info {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(47, 131, 255, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 100%;
  line-height: 50rpx;
  margin-top: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

.paid-money {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.magin-bottom {
  margin-bottom: 20rpx;
}

.order-details {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 60rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
}
.flex1-clz {
  border-top: 2rpx solid #e4e4e4;
  padding: 16rpx 32rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  bottom: 0rpx;
  background-color: #ffffff;
  box-shadow: 0rpx 4rpx 12rpx rgba(31, 31, 31, 0.16);
  overflow: visible;
  left: 0rpx;
}
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.action-btn {
  padding: 16rpx 28rpx;
  border-radius: 5rpx;
  font-size: 32rpx;
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
  border-radius: 40rpx;
  margin-left: 28rpx;
}

.action-btn:nth-last-child(1) {
  background: rgba(47, 131, 255, 0.2);
  color: rgba(47, 131, 255, 1);
  border-radius: 40rpx;
}

.review-btn {
  background: rgba(255, 165, 0, 1) !important;
  color: white !important;
}
.more-btn {
  margin-right: 20rpx;
  font-size: 32rpx;
  position: relative;
  color: rgba(102, 102, 102, 1);
}
.more-actions-dropdown {
  position: absolute;
  bottom: 110rpx;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

.hidden {
  display: none;
}

/* 功能按钮区域样式 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.action-buttons .action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 10rpx;
  min-height: 100rpx;
  transition: all 0.3s ease;
  margin-left: 0;
}

.contact-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.reschedule-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.action-buttons .action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 为底部按钮留出空间 */
.container {
  padding-bottom: 160rpx;
}

/* 原价样式 */
.original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 28rpx;
}
