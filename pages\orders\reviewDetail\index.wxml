<view class="container">
  <!-- 导航栏 -->
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60' isBack="{{true}}">
    <view slot="content">
      <text class="navbar-title">评价详情</text>
    </view>
  </diy-navbar>

  <view class="content">
    <!-- 评价信息卡片 -->
    <view class="review-card">
      <view class="review-header">
        <text class="review-title">客户评价</text>
        <view class="rating-container">
          <view class="stars">
            <text wx:for="{{reviewDetail.ratingStars}}" wx:key="index" 
                  class="star {{item.type === 'full' ? 'star-full' : item.type === 'half' ? 'star-half' : 'star-empty'}}">
              ★
            </text>
          </view>
          <text class="rating-text">{{reviewDetail.rating || 0}}分</text>
        </view>
      </view>

      <!-- 评价内容 -->
      <view class="review-content" wx:if="{{reviewDetail.comment}}">
        <view class="content-label">评价内容：</view>
        <view class="content-text">{{reviewDetail.comment}}</view>
      </view>

      <!-- 评价时间 -->
      <view class="review-time" wx:if="{{reviewDetail.createdAt}}">
        <text class="time-label">评价时间：</text>
        <text class="time-text">{{reviewDetail.createdAt}}</text>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-card" wx:if="{{reviewDetail.orderId}}">
      <view class="card-title">相关订单</view>
      <view class="info-item">
        <text class="info-label">订单ID：</text>
        <text class="info-value">{{reviewDetail.orderId}}</text>
      </view>
      <view class="info-item" wx:if="{{reviewDetail.customerId}}">
        <text class="info-label">客户ID：</text>
        <text class="info-value">{{reviewDetail.customerId}}</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!reviewDetail.rating && !reviewDetail.comment}}">
      <image src="//xian7.zos.ctyun.cn/pet/static/noreder.png" class="empty-image"></image>
      <text class="empty-text">暂无评价信息</text>
    </view>
  </view>
</view>
