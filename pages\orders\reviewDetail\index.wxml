<view class="container">
  <!-- 导航栏 -->
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60' isBack="{{true}}">
    <view slot="content">
      <text class="navbar-title">评价</text>
    </view>
  </diy-navbar>

  <view class="content">
    <!-- 服务评分卡片 -->
    <view class="rating-card">
      <view class="rating-header">
        <text class="rating-label">服务评分</text>
        <view class="rating-score">
          <view class="stars">
            <text wx:for="{{reviewDetail.ratingStars}}" wx:key="index"
                  class="star {{item.type === 'full' ? 'star-full' : item.type === 'half' ? 'star-half' : 'star-empty'}}">
              ★
            </text>
          </view>
          <text class="score-text">{{reviewDetail.rating || 0}}</text>
        </view>
      </view>
    </view>

    <!-- 评价内容卡片 -->
    <view class="comment-card" wx:if="{{reviewDetail.comment}}">
      <view class="comment-text">{{reviewDetail.comment}}</view>
    </view>

    <!-- 评价图片卡片 -->
    <view class="images-card">
      <!-- 如果有图片，显示图片 -->
      <block wx:if="{{reviewDetail.images && reviewDetail.images.length > 0}}">
        <view class="image-item" wx:for="{{reviewDetail.images}}" wx:key="index">
          <image src="{{item}}" class="review-image" mode="aspectFill" bindtap="previewImage" data-url="{{item}}" data-urls="{{reviewDetail.images}}"></image>
        </view>
      </block>
      <!-- 如果没有图片，显示占位图 -->
      <block wx:else>
        <view class="image-item">
          <image src="//xian7.zos.ctyun.cn/pet/static/memberAvatar.png" class="review-image" mode="aspectFill"></image>
        </view>
        <view class="image-placeholder">
          <view class="placeholder-icon">📷</view>
          <text class="placeholder-text">上传照片</text>
        </view>
      </block>
    </view>

    <!-- 评价时间 -->
    <view class="time-info" wx:if="{{reviewDetail.createdAt}}">
      <text class="time-text">{{reviewDetail.createdAt}}</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!reviewDetail.rating && !reviewDetail.comment}}">
      <image src="//xian7.zos.ctyun.cn/pet/static/noreder.png" class="empty-image"></image>
      <text class="empty-text">暂无评价信息</text>
    </view>
  </view>
</view>
