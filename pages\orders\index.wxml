<view class="container containermessage">
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60'>
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-start diygw-col-24 title-tab">
        <text
          wx:for="{{orderTypeTabs}}"
          wx:key="type"
          class="titleTab {{currentOrderType === item.type ? 'titleTabActive' : ''}}"
          bindtap="switchOrderType"
          data-type="{{item.type}}"
        >
          {{item.name}}
        </text>
      </view>
    </view>
  </diy-navbar>
  <view class="contentmessage">
    <!-- 订单状态切换标签 -->
    <view class="order-tabs">
      <view wx:for="{{orderTabs}}" wx:key="status" class="tab-item {{currentTab === item.status ? 'active' : ''}}" bindtap="switchTab" data-status="{{item.status}}">
        {{item.name}}
      </view>
    </view>
    <!-- 订单列表 -->
    <scroll-view scroll-y class="order-list" bindscrolltolower="loadMoreOrders">
      <block wx:if="{{orderList.length > 0}}">
        <view wx:for="{{orderList}}" wx:key="id" class="order-item">
          <view class="order-content" data-id="{{item.id}}" bind:tap="viewOrderDetail">
            <image class="product-image" src="{{item.productImage}}"></image>
            <view class="product-info">
              <view class="flex align-center justify-between">
                <text class="product-name">{{item.productName}}</text>
                <view class="price-container">
                  <text wx:if="{{item.originalPrice && item.originalPrice > 0}}" class="original-price">¥{{item.originalPrice}}</text>
                  <text class="total-fee">¥{{item.totalFee || '0.00'}}</text>
                </view>
              </view>
              <view class="flex align-center justify-between">
                <text wx:if="{{item.extraServive.length > 0}}" class="product-service">增项服务：<text wx:for="{{item.extraServive}}" wx:for-item="val" wx:key="val">{{val}}{{index < item.extraServive.length-1 ? '、':''}}</text>
                </text>
                <text wx:else class="product-service">增项服务：无</text>
              </view>
            </view>
          </view>
          <view class="flex align-center justify-between magin-bottom">
            <text>服务宠物：</text>
            <text>{{item.petName}}</text>
          </view>
          <view class="flex align-center justify-between magin-bottom">
            <text>期待上门时间：</text>
            <text>{{item.expectTime}}</text>
          </view>
          <view class="flex align-center justify-between magin-bottom">
            <text>服务地址：</text>
            <text>{{item.userAdress}}</text>
          </view>

          <view class="flex align-center justify-between">
            <!-- <view class="more-btn" bindtap="toggleOrderActions" data-id="{{item.id}}">更多</view> -->
            <view class='flex order-actions'>
              <!-- 根据订单状态显示不同的操作按钮 -->
              <view wx:if="{{item.status === '待服务'}}" class="action-btn" bindtap="reschedule" data-id="{{item.id}}">
                更改时间
              </view>
              <block wx:if="{{item.status === '待服务'}}">
                <view class="action-btn" bindtap="dispatch" data-id="{{item.id}}">
                  出发
                </view>
              </block>

              <block wx:if="{{item.status === '已出发'}}">
                <view class="action-btn blue-btn" bindtap="start" data-id="{{item.id}}">
                  开始服务
                </view>
              </block>

              <block wx:if="{{item.status === '服务中'}}">
                <view class="action-btn blue-btn" bindtap="complete" data-id="{{item.id}}">
                  结束服务
                </view>
              </block>
            </view>
          </view>

          <!-- 更多操作弹窗 -->
          <view wx:if="{{item.showMoreActions}}" class="more-actions-dropdown">
            <view class="dropdown-item" bindtap="viewOrderDetail" data-id="{{item.id}}">
              更改服务地址
            </view>
            <view class="dropdown-item" bindtap="deleteOrder" data-id="{{item.id}}">
              更换服务人员
            </view>
            <view class="dropdown-item" bindtap="toggleOrderActions" data-id="{{item.id}}">
              取消订单
            </view>
          </view>
        </view>
      </block>

      <view wx:else class="empty-list">
        <image src="//xian7.zos.ctyun.cn/pet/static/noreder.png" class="empty-image"></image>
        <text class="empty-text">暂无订单</text>
      </view>
    </scroll-view>
  </view>
  <!-- 时间选择器 -->
  <custom-picker
    wx:if="{{showTimePicker}}"
    bind:confirm="onTimeSelected"
    bind:cancel="onTimeCancel"
    selectedTime="{{selectedTime}}"
  />
  <custom-tabbar currentActive='service' />
</view>