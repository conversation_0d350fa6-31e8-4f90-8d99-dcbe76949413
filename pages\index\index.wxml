<view class="container containermessage">
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60'>
    <view slot="content">
      <view class="flex align-center flex-nowrap diygw-col-24">
        <image src="//xian7.zos.ctyun.cn/pet/static/logo.png" class="response diygw-col-10" mode="widthFix"></image>
      </view>
    </view>
  </diy-navbar>
  <view class="contentmessage">
    <!-- 订单状态切换标签 -->
    <view class="order-tabs">
      <view wx:for="{{orderTabs}}" wx:key="status" class="tab-item {{currentTab === item.status ? 'active' : ''}}" bindtap="switchTab" data-status="{{item.status}}">
        {{item.name}}
      </view>
    </view>
    <!-- 订单列表 -->
    <scroll-view scroll-y class="order-list" bindscrolltolower="loadMoreOrders">
      <block wx:if="{{orderList.length > 0}}">
        <view wx:for="{{orderList}}" wx:key="orderId" class="order-item">
          <view class="order-header">
            <text class="order-number">订单号: {{item.sn}}</text>
            <text class="order-status">{{item.status}}</text>
          </view>
          <view class="order-content" data-item="{{item}}" bind:tap="viewOrderDetail">
            <image class="product-image" src="{{item.serviceLogo}}"></image>
            <view class="product-info">
              <view class="flex align-center justify-between">
                <text class="product-name">{{item.serviceName}}</text>
                <view class="price-container">
                  <text wx:if="{{item.originalPrice && item.originalPrice > 0}}" class="original-price">¥{{item.originalPrice}}</text>
                  <text class="total-fee">¥{{item.totalFee || '0.00'}}</text>
                </view>
              </view>
              <view class="flex flex-direction-column justify-between">
                <view class="flex align-baseline justify-start magin-bottom">
                  <text class="flex icon6 diygw-col-0 diy-icon-remind"></text>
                  <text>{{item.orderTime}}</text>
                </view>
                <view class="flex align-baseline justify-start magin-bottom">
                  <text class="flex icon6 diygw-col-0 diy-icon-location"></text>
                  <text>{{item.addressDetail}}</text>
                </view>
              </view>
            </view>
          </view>
          <view class="flex align-center justify-between">
            <!-- 根据订单状态显示不同的操作按钮 -->
            <view class=" diygw-col-24 action-btn" bindtap="reschedule" data-order-id="{{item.id}}">
              立即接单
            </view>
          </view>
        </view>
      </block>

      <view wx:else class="empty-list">
        <image src="//xian7.zos.ctyun.cn/pet/static/noreder.png" class="empty-image"></image>
        <text class="empty-text">暂无订单</text>
      </view>
    </scroll-view>
  </view>
  <!-- 接单确认弹窗 -->
  <custom-modal
    show="{{showConfirmModal}}"
    title="{{confirmModalTitle}}"
    content="{{confirmModalContent}}"
    confirmText="{{confirmModalBtnText}}"
    showCancel="{{true}}"
    cancelText="取消"
    bind:confirm="onConfirmAccept"
    bind:cancel="onCancelAccept"
  />
  <custom-tabbar currentActive='home' />
</view>