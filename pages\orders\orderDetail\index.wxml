<view class="container">
  <view class="order-info">
    <view class="order-content" data-orderId="{{orderDetail.id}}" bind:tap="viewOrderDetail">
      <image class="product-image" src="{{orderDetail.productImage}}"></image>
      <view class="product-info">
        <view class="flex align-center justify-between">
          <text class="product-name">{{orderDetail.productName}}</text>
        </view>
        <view class="flex align-center justify-between">
          <text class="product-service">增项服务：<text wx:for="{{orderDetail.additionalServices}}" wx:for-item="val" wx:key="val">{{val}}{{index < orderDetail.additionalServices.length-1? '、':''}}</text>
          </text>
        </view>
      </view>
    </view>

    <view class="order-details">
      <view class="detail-item">
        <text class="label">订单编号</text>
        <text class="content">{{orderDetail.sn}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务宠物</text>
        <text class="content">{{orderDetail.petName}}</text>
      </view>
      <view class="detail-item">
        <text class="label">宠物主人</text>
        <text class="content">{{orderDetail.customer.nickname || orderDetail.customer.name}}</text>
      </view>
      <view class="detail-item">
        <text class="label">联系电话</text>
        <text class="content">{{orderDetail.customer.phone || orderDetail.customer.mobile}}</text>
      </view>
      <view class="detail-item">
        <text class="label">期望上门时间</text>
        <text class="content">{{orderDetail.serviceTime}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务地址</text>
        <text class="content">{{orderDetail.addressDetail}}</text>
      </view>
      <view class="detail-item">
        <text class="label">最近出入口</text>
        <text class="content">{{orderDetail.addressRemark}}</text>
      </view>
      <view class="detail-item">
        <text class="label">创建时间</text>
        <text class="content">{{orderDetail.createdAt}}</text>
      </view>
      <!-- 金额信息 -->
      <view class="detail-item" wx:if="{{orderDetail.originalPrice}}">
        <text class="label">原价</text>
        <text class="content original-price">¥{{orderDetail.originalPrice}}</text>
      </view>
      <view class="detail-item">
        <text class="label">实付金额</text>
        <text class="content paid-money">{{orderDetail.totalFee}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">付款时间</text>
        <text class="content">{{orderDetail.orderNumber}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">服务时间</text>
        <text class="content">{{orderDetail.orderNumber}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">成交时间</text>
        <text class="content">{{orderDetail.orderNumber}}</text>
      </view>
    </view>
  </view>
  <!-- 更多操作弹窗 -->
  <view wx:if="{{showMoreActions}}" class="more-actions-dropdown">
    <view class="dropdown-item" bindtap="viewOrderDetail" data-order-id="{{item.orderId}}">
      更改服务地址
    </view>
    <view class="dropdown-item" bindtap="deleteOrder" data-order-id="{{item.orderId}}">
      更换服务人员
    </view>
    <view class="dropdown-item" bindtap="toggleOrderActions" data-order-id="{{item.orderId}}">
      取消订单
    </view>
  </view>
  <view class="diygw-col-24 diygw-bottom flex1-clz hidden">
    <view class="flex align-center justify-between">
      <view class="more-btn" bindtap="toggleOrderActions" data-order-id="{{orderDetail.orderId}}">更多</view>
      <view class='order-actions'>
        <view class="action-btn" bindtap="reschedule" data-order-id="{{orderDetail.orderId}}">
          更改时间
        </view>
        <block wx:if="{{orderDetail.status === 'unpaid'}}">
          <view class="action-btn" bindtap="payOrder" data-order-id="{{orderDetail.orderId}}">
            去付款
          </view>
        </block>

        <block wx:if="{{orderDetail.status === 'paid'}}">
          <view class="action-btn blue-btn" bindtap="confirmReceipt" data-order-id="{{orderDetail.orderId}}">
            催接单
          </view>
        </block>

        <block wx:if="{{orderDetail.status === 'completed'}}">
          <view class="action-btn blue-btn" bindtap="reviewOrder" data-order-id="{{orderDetail.orderId}}">
            去评价
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 功能按钮区域 - 只有待服务状态才显示 -->
  <view class="action-buttons" wx:if="{{orderDetail.status === '待服务'}}">
    <view class="action-btn contact-btn" bindtap="contactCustomer">
      <text class="btn-icon">📞</text>
      <text class="btn-text">联系客户</text>
    </view>
    <view class="action-btn reschedule-btn" bindtap="reschedule">
      <text class="btn-icon">⏰</text>
      <text class="btn-text">修改上门时间</text>
    </view>
  </view>

  <!-- 时间选择器 -->
  <custom-picker
    wx:if="{{showTimePicker}}"
    bind:confirm="onTimeSelected"
    bind:cancel="onTimeCancel"
    selectedTime="{{selectedTime}}"
  />
</view>