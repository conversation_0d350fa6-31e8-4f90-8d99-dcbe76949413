import { formatNormalDate } from '../../utils/util';
import reviewApi from '../../../api/modules/review';

Page({
  data: {
    reviewDetail: {}, // 评价详情
    orderId: '', // 订单ID
  },

  onLoad(options) {
    // 获取订单ID
    if (options.orderId) {
      this.setData({ orderId: options.orderId });
    }

    // 从本地存储获取评价信息
    const reviewInfo = wx.getStorageSync('reviewInfo');
    if (reviewInfo) {
      this.setReviewDetail(reviewInfo);
    } else if (options.orderId) {
      // 如果本地存储没有，则通过API获取
      this.loadReviewDetail(options.orderId);
    }
  },

  // 设置评价详情数据
  setReviewDetail(reviewInfo) {
    // 处理评价图片数据
    let images = [];
    if (reviewInfo.images) {
      if (typeof reviewInfo.images === 'string') {
        // 如果是字符串，尝试解析JSON
        try {
          images = JSON.parse(reviewInfo.images);
        } catch (e) {
          // 如果解析失败，按逗号分割
          images = reviewInfo.images.split(',').filter(img => img.trim());
        }
      } else if (Array.isArray(reviewInfo.images)) {
        images = reviewInfo.images;
      }
    }

    this.setData({
      reviewDetail: {
        ...reviewInfo,
        // 格式化时间
        createdAt: reviewInfo.createdAt ? formatNormalDate(reviewInfo.createdAt) : '',
        updatedAt: reviewInfo.updatedAt ? formatNormalDate(reviewInfo.updatedAt) : '',
        // 格式化评分显示
        ratingStars: this.generateStars(reviewInfo.rating || 0),
        // 处理图片数据
        images: images,
      },
    });
  },

  // 通过API加载评价详情
  async loadReviewDetail(orderId) {
    wx.showLoading({ title: '加载中...' });

    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      if (reviewData && reviewData.data) {
        this.setReviewDetail(reviewData.data);
        // 更新本地存储
        wx.setStorageSync('reviewInfo', reviewData.data);
      } else {
        wx.showToast({
          title: '暂无评价信息',
          icon: 'none',
        });
        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载评价详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      wx.hideLoading();
    }
  },

  // 生成星级评分显示
  generateStars(rating) {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    // 添加实心星星
    for (let i = 0; i < fullStars; i++) {
      stars.push({ type: 'full' });
    }

    // 添加半星
    if (hasHalfStar) {
      stars.push({ type: 'half' });
    }

    // 添加空心星星
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push({ type: 'empty' });
    }

    return stars;
  },

  // 预览图片
  previewImage(e) {
    const { url, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: urls || [url],
    });
  },
});
